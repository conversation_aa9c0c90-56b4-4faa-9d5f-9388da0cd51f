import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';

function App() {
  return (
    <Router>
      <div className="App">
        <Routes>
          <Route path="/" element={
            <div style={{padding: '20px'}}>
              <h1 style={{color: 'blue', fontSize: '32px', marginBottom: '20px'}}>YouTube Video Summarizer</h1>
              <p style={{fontSize: '18px', marginBottom: '20px'}}>Transform YouTube videos into instant summaries</p>
              <button style={{
                backgroundColor: '#3b82f6',
                color: 'white',
                padding: '12px 24px',
                border: 'none',
                borderRadius: '8px',
                fontSize: '16px',
                cursor: 'pointer'
              }}>
                Get Started
              </button>
            </div>
          } />
          <Route path="/login" element={
            <div style={{padding: '20px'}}>
              <h1 style={{color: 'green'}}>Login Page</h1>
              <p>This will be the login form</p>
            </div>
          } />
          <Route path="/dashboard" element={
            <div style={{padding: '20px'}}>
              <h1 style={{color: 'purple'}}>Dashboard</h1>
              <p>This will be the user dashboard</p>
            </div>
          } />
        </Routes>
      </div>
    </Router>
  );
}

export default App;
