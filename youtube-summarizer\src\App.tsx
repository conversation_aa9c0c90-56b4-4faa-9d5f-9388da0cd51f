import React, { useState } from 'react';

function App() {
  const [currentPage, setCurrentPage] = useState('home');

  const buttonStyle = {
    backgroundColor: '#3b82f6',
    color: 'white',
    padding: '12px 24px',
    border: 'none',
    borderRadius: '8px',
    fontSize: '16px',
    cursor: 'pointer',
    margin: '5px'
  };

  const navStyle = {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: '20px',
    backgroundColor: 'white',
    borderBottom: '1px solid #e5e7eb',
    marginBottom: '0'
  };

  const containerStyle = {
    maxWidth: '1200px',
    margin: '0 auto',
    padding: '0 20px'
  };

  if (currentPage === 'login') {
    return (
      <div>
        <nav style={navStyle}>
          <div style={{fontSize: '24px', fontWeight: 'bold', color: '#1f2937'}}>
            📹 VideoSummarizer
          </div>
          <button
            style={{...buttonStyle, backgroundColor: '#6b7280'}}
            onClick={() => setCurrentPage('home')}
          >
            Back to Home
          </button>
        </nav>
        <div style={{...containerStyle, padding: '40px 20px'}}>
          <div style={{maxWidth: '400px', margin: '0 auto', backgroundColor: 'white', padding: '40px', borderRadius: '12px', boxShadow: '0 4px 6px rgba(0,0,0,0.1)'}}>
            <h2 style={{fontSize: '28px', marginBottom: '20px', textAlign: 'center', color: '#1f2937'}}>Welcome Back</h2>
            <p style={{textAlign: 'center', color: '#6b7280', marginBottom: '30px'}}>Sign in to your account</p>

            <div style={{marginBottom: '20px'}}>
              <label style={{display: 'block', marginBottom: '8px', fontWeight: '500', color: '#374151'}}>Email</label>
              <input
                type="email"
                placeholder="Enter your email"
                style={{
                  width: '100%',
                  padding: '12px',
                  border: '1px solid #d1d5db',
                  borderRadius: '8px',
                  fontSize: '16px',
                  boxSizing: 'border-box'
                }}
              />
            </div>

            <div style={{marginBottom: '30px'}}>
              <label style={{display: 'block', marginBottom: '8px', fontWeight: '500', color: '#374151'}}>Password</label>
              <input
                type="password"
                placeholder="Enter your password"
                style={{
                  width: '100%',
                  padding: '12px',
                  border: '1px solid #d1d5db',
                  borderRadius: '8px',
                  fontSize: '16px',
                  boxSizing: 'border-box'
                }}
              />
            </div>

            <button
              style={{...buttonStyle, width: '100%', marginBottom: '20px'}}
              onClick={() => setCurrentPage('dashboard')}
            >
              Sign In
            </button>

            <p style={{textAlign: 'center', color: '#6b7280'}}>
              Don't have an account? <span style={{color: '#3b82f6', cursor: 'pointer'}}>Sign up</span>
            </p>
          </div>
        </div>
      </div>
    );
  }

  if (currentPage === 'dashboard') {
    return (
      <div>
        <nav style={navStyle}>
          <div style={{fontSize: '24px', fontWeight: 'bold', color: '#1f2937'}}>
            📹 VideoSummarizer
          </div>
          <div>
            <button
              style={{...buttonStyle, backgroundColor: '#6b7280', marginRight: '10px'}}
              onClick={() => setCurrentPage('home')}
            >
              Home
            </button>
            <button
              style={{...buttonStyle, backgroundColor: '#ef4444'}}
              onClick={() => setCurrentPage('home')}
            >
              Logout
            </button>
          </div>
        </nav>
        <div style={{...containerStyle, padding: '40px 20px'}}>
          <h1 style={{fontSize: '32px', marginBottom: '10px', color: '#1f2937'}}>Dashboard</h1>
          <p style={{color: '#6b7280', marginBottom: '40px'}}>Transform YouTube videos into instant summaries</p>

          {/* Stats */}
          <div style={{display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '20px', marginBottom: '40px'}}>
            <div style={{backgroundColor: 'white', padding: '24px', borderRadius: '12px', boxShadow: '0 2px 4px rgba(0,0,0,0.1)'}}>
              <h3 style={{color: '#1f2937', marginBottom: '8px'}}>Total Summaries</h3>
              <p style={{fontSize: '32px', fontWeight: 'bold', color: '#3b82f6', margin: '0'}}>24</p>
            </div>
            <div style={{backgroundColor: 'white', padding: '24px', borderRadius: '12px', boxShadow: '0 2px 4px rgba(0,0,0,0.1)'}}>
              <h3 style={{color: '#1f2937', marginBottom: '8px'}}>Time Saved</h3>
              <p style={{fontSize: '32px', fontWeight: 'bold', color: '#10b981', margin: '0'}}>8.5h</p>
            </div>
            <div style={{backgroundColor: 'white', padding: '24px', borderRadius: '12px', boxShadow: '0 2px 4px rgba(0,0,0,0.1)'}}>
              <h3 style={{color: '#1f2937', marginBottom: '8px'}}>Downloads</h3>
              <p style={{fontSize: '32px', fontWeight: 'bold', color: '#8b5cf6', margin: '0'}}>18</p>
            </div>
          </div>

          {/* New Summary Form */}
          <div style={{backgroundColor: 'white', padding: '30px', borderRadius: '12px', boxShadow: '0 2px 4px rgba(0,0,0,0.1)', marginBottom: '30px'}}>
            <h2 style={{fontSize: '24px', marginBottom: '20px', color: '#1f2937'}}>Create New Summary</h2>
            <div style={{display: 'flex', gap: '15px', flexWrap: 'wrap'}}>
              <input
                type="url"
                placeholder="Paste YouTube video URL here..."
                style={{
                  flex: '1',
                  minWidth: '300px',
                  padding: '15px',
                  border: '1px solid #d1d5db',
                  borderRadius: '8px',
                  fontSize: '16px'
                }}
              />
              <button style={{...buttonStyle, padding: '15px 30px'}}>
                Generate Summary
              </button>
            </div>
          </div>

          {/* Recent Summaries */}
          <div style={{backgroundColor: 'white', padding: '30px', borderRadius: '12px', boxShadow: '0 2px 4px rgba(0,0,0,0.1)'}}>
            <h2 style={{fontSize: '24px', marginBottom: '20px', color: '#1f2937'}}>Recent Summaries</h2>
            <div style={{space: '15px'}}>
              {[
                {title: 'How to Build a React App with TypeScript', status: 'completed', time: '2 hours ago'},
                {title: 'Advanced JavaScript Concepts Explained', status: 'processing', time: '1 hour ago'},
                {title: 'CSS Grid vs Flexbox: Complete Guide', status: 'completed', time: '3 hours ago'}
              ].map((item, index) => (
                <div key={index} style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  padding: '15px',
                  border: '1px solid #e5e7eb',
                  borderRadius: '8px',
                  marginBottom: '10px'
                }}>
                  <div>
                    <h3 style={{margin: '0 0 5px 0', color: '#1f2937'}}>{item.title}</h3>
                    <p style={{margin: '0', color: '#6b7280', fontSize: '14px'}}>{item.time}</p>
                  </div>
                  <div>
                    {item.status === 'completed' ? (
                      <span style={{color: '#10b981', fontWeight: '500'}}>✅ Completed</span>
                    ) : (
                      <span style={{color: '#f59e0b', fontWeight: '500'}}>⏳ Processing</span>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Home page
  return (
    <div>
      {/* Navigation */}
      <nav style={navStyle}>
        <div style={{fontSize: '24px', fontWeight: 'bold', color: '#1f2937'}}>
          📹 VideoSummarizer
        </div>
        <div>
          <button
            style={{...buttonStyle, backgroundColor: '#6b7280', marginRight: '10px'}}
            onClick={() => setCurrentPage('home')}
          >
            Features
          </button>
          <button
            style={{...buttonStyle, backgroundColor: '#6b7280', marginRight: '10px'}}
            onClick={() => setCurrentPage('home')}
          >
            Pricing
          </button>
          <button
            style={buttonStyle}
            onClick={() => setCurrentPage('login')}
          >
            Sign In
          </button>
        </div>
      </nav>

      {/* Hero Section */}
      <div style={{background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)', color: 'white', padding: '80px 20px', textAlign: 'center'}}>
        <div style={containerStyle}>
          <h1 style={{fontSize: '48px', marginBottom: '20px', fontWeight: 'bold'}}>
            Transform YouTube Videos into
            <br />
            <span style={{color: '#fbbf24'}}>Instant Summaries</span>
          </h1>
          <p style={{fontSize: '20px', marginBottom: '40px', opacity: '0.9'}}>
            Extract key insights, download captions, and get AI-powered summaries from any YouTube video in seconds.
          </p>
          <button
            style={{...buttonStyle, fontSize: '18px', padding: '15px 30px', backgroundColor: '#fbbf24', color: '#1f2937'}}
            onClick={() => setCurrentPage('login')}
          >
            Get Started Free
          </button>
        </div>
      </div>

      {/* Features Section */}
      <div style={{padding: '80px 20px', backgroundColor: 'white'}}>
        <div style={containerStyle}>
          <h2 style={{fontSize: '36px', textAlign: 'center', marginBottom: '50px', color: '#1f2937'}}>
            Powerful Features for Content Creators
          </h2>
          <div style={{display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', gap: '40px'}}>
            <div style={{textAlign: 'center', padding: '30px'}}>
              <div style={{fontSize: '48px', marginBottom: '20px'}}>✨</div>
              <h3 style={{fontSize: '24px', marginBottom: '15px', color: '#1f2937'}}>AI-Powered Summaries</h3>
              <p style={{color: '#6b7280', lineHeight: '1.6'}}>
                Get intelligent, contextual summaries that capture the key points and insights from any video content.
              </p>
            </div>
            <div style={{textAlign: 'center', padding: '30px'}}>
              <div style={{fontSize: '48px', marginBottom: '20px'}}>📥</div>
              <h3 style={{fontSize: '24px', marginBottom: '15px', color: '#1f2937'}}>Caption Download</h3>
              <p style={{color: '#6b7280', lineHeight: '1.6'}}>
                Download closed captions and subtitles in multiple formats for offline access and further processing.
              </p>
            </div>
            <div style={{textAlign: 'center', padding: '30px'}}>
              <div style={{fontSize: '48px', marginBottom: '20px'}}>📝</div>
              <h3 style={{fontSize: '24px', marginBottom: '15px', color: '#1f2937'}}>Smart Transcription</h3>
              <p style={{color: '#6b7280', lineHeight: '1.6'}}>
                Advanced transcription technology that understands context and provides accurate text conversion.
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Pricing Section */}
      <div style={{padding: '80px 20px', backgroundColor: '#f8fafc'}}>
        <div style={containerStyle}>
          <h2 style={{fontSize: '36px', textAlign: 'center', marginBottom: '50px', color: '#1f2937'}}>
            Simple, Transparent Pricing
          </h2>
          <div style={{display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', gap: '30px', maxWidth: '1000px', margin: '0 auto'}}>
            {[
              {
                name: 'Free',
                price: '$0',
                period: 'forever',
                features: ['5 video summaries per month', 'Basic caption download', 'Standard summary quality', 'Email support'],
                popular: false
              },
              {
                name: 'Pro',
                price: '$19',
                period: 'per month',
                features: ['100 video summaries per month', 'Advanced caption download', 'High-quality AI summaries', 'Priority support', 'Custom templates'],
                popular: true
              },
              {
                name: 'Enterprise',
                price: '$99',
                period: 'per month',
                features: ['Unlimited video summaries', 'Premium features', 'API access', 'Dedicated support', 'Team collaboration'],
                popular: false
              }
            ].map((plan, index) => (
              <div key={index} style={{
                backgroundColor: 'white',
                padding: '40px 30px',
                borderRadius: '12px',
                boxShadow: plan.popular ? '0 8px 25px rgba(59, 130, 246, 0.15)' : '0 4px 6px rgba(0,0,0,0.1)',
                border: plan.popular ? '2px solid #3b82f6' : '1px solid #e5e7eb',
                position: 'relative',
                textAlign: 'center'
              }}>
                {plan.popular && (
                  <div style={{
                    position: 'absolute',
                    top: '-12px',
                    left: '50%',
                    transform: 'translateX(-50%)',
                    backgroundColor: '#3b82f6',
                    color: 'white',
                    padding: '6px 20px',
                    borderRadius: '20px',
                    fontSize: '14px',
                    fontWeight: '500'
                  }}>
                    Most Popular
                  </div>
                )}
                <h3 style={{fontSize: '24px', marginBottom: '10px', color: '#1f2937'}}>{plan.name}</h3>
                <div style={{marginBottom: '30px'}}>
                  <span style={{fontSize: '48px', fontWeight: 'bold', color: '#1f2937'}}>{plan.price}</span>
                  <span style={{color: '#6b7280', marginLeft: '8px'}}>/{plan.period}</span>
                </div>
                <ul style={{listStyle: 'none', padding: '0', marginBottom: '30px'}}>
                  {plan.features.map((feature, featureIndex) => (
                    <li key={featureIndex} style={{
                      padding: '8px 0',
                      color: '#374151',
                      borderBottom: featureIndex < plan.features.length - 1 ? '1px solid #f3f4f6' : 'none'
                    }}>
                      ✓ {feature}
                    </li>
                  ))}
                </ul>
                <button
                  style={{
                    ...buttonStyle,
                    width: '100%',
                    backgroundColor: plan.popular ? '#3b82f6' : '#6b7280'
                  }}
                  onClick={() => setCurrentPage('login')}
                >
                  {plan.name === 'Free' ? 'Get Started' : 'Start Free Trial'}
                </button>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Footer */}
      <footer style={{backgroundColor: '#1f2937', color: 'white', padding: '40px 20px', textAlign: 'center'}}>
        <div style={containerStyle}>
          <div style={{fontSize: '24px', fontWeight: 'bold', marginBottom: '20px'}}>
            📹 VideoSummarizer
          </div>
          <p style={{color: '#9ca3af', marginBottom: '20px'}}>
            Transform YouTube videos into instant summaries with AI-powered technology.
          </p>
          <p style={{color: '#6b7280', fontSize: '14px'}}>
            © 2024 VideoSummarizer. All rights reserved.
          </p>
        </div>
      </footer>
    </div>
  );
}

export default App;
